"""
多AI模型助手
支持OpenAI、千问、火山云等多个AI提供商
"""
import os
import time
import random
import logging
from typing import Optional, Dict, Any
from .config import config

# 配置日志
logger = logging.getLogger(__name__)

class MultiAIHelper:
    """多AI模型助手类，支持多个提供商"""

    def __init__(self, provider: str = None, max_retries: int = None, retry_delay: float = 1.0):
        self.provider = provider or config.AI_PROVIDER
        self.max_retries = max_retries or config.AI_MAX_RETRIES
        self.retry_delay = retry_delay
        self.temperature = config.AI_TEMPERATURE
        self.max_tokens = config.AI_MAX_TOKENS
        
        # 初始化客户端
        self.clients = {}
        self._init_clients()

    def _init_clients(self):
        """初始化各个AI提供商的客户端"""
        # OpenAI客户端
        try:
            from openai import OpenAI
            if config.OPENAI_API_KEY:
                self.clients['openai'] = OpenAI(
                    api_key=config.OPENAI_API_KEY,
                    base_url=config.OPENAI_BASE_URL
                )
                logger.info("OpenAI客户端初始化成功")
        except ImportError:
            logger.warning("OpenAI库未安装，无法使用OpenAI模型")
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {e}")

        # 千问客户端
        try:
            import dashscope
            if config.QWEN_API_KEY:
                dashscope.api_key = config.QWEN_API_KEY
                self.clients['qwen'] = dashscope
                logger.info("千问客户端初始化成功")
            else:
                logger.info("千问API密钥未配置，跳过初始化")
        except ImportError:
            logger.info("dashscope库未安装，无法使用千问模型")
        except Exception as e:
            logger.warning(f"千问客户端初始化失败: {e}")

        # 火山云客户端
        try:
            if config.VOLCENGINE_API_KEY:
                # 火山云使用简单的HTTP请求方式，不需要特殊的客户端初始化
                endpoint = config.VOLCENGINE_ENDPOINT
                # 如果端点不是完整URL，则添加默认前缀
                if endpoint and not endpoint.startswith('http'):
                    endpoint = f'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
                elif not endpoint:
                    endpoint = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'

                self.clients['volcengine'] = {
                    'api_key': config.VOLCENGINE_API_KEY,
                    'endpoint': endpoint
                }
                logger.info(f"火山云客户端初始化成功，端点: {endpoint}")
            else:
                logger.info("火山云API密钥未配置，跳过初始化")
        except Exception as e:
            logger.warning(f"火山云客户端初始化失败: {e}")

    def _call_openai(self, messages: list) -> Optional[str]:
        """调用OpenAI API"""
        if 'openai' not in self.clients:
            raise Exception("OpenAI客户端未初始化")

        client = self.clients['openai']
        # 使用当前配置的模型
        model = config.GPT_MODEL

        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=config.AI_TEMPERATURE,
            max_tokens=config.AI_MAX_TOKENS,
            timeout=30
        )

        if response and response.choices and len(response.choices) > 0:
            content = response.choices[0].message.content
            if content and content.strip():
                return content.strip()
        return None

    def _call_qwen(self, messages: list) -> Optional[str]:
        """调用千问API"""
        if 'qwen' not in self.clients:
            raise Exception("千问客户端未初始化")

        dashscope = self.clients['qwen']
        # 使用当前配置的模型
        model = config.QWEN_MODEL

        # 转换消息格式
        qwen_messages = []
        for msg in messages:
            qwen_messages.append({
                'role': msg['role'],
                'content': msg['content']
            })

        response = dashscope.Generation.call(
            model=model,
            messages=qwen_messages,
            temperature=config.AI_TEMPERATURE,
            max_tokens=config.AI_MAX_TOKENS,
            result_format='message'
        )

        if response.status_code == 200:
            content = response.output.choices[0].message.content
            if content and content.strip():
                return content.strip()
        else:
            raise Exception(f"千问API调用失败: {response.message}")
        return None

    def _call_volcengine(self, messages: list) -> Optional[str]:
        """调用火山云API"""
        if 'volcengine' not in self.clients:
            raise Exception("火山云客户端未初始化")

        # 火山云API调用（这里需要根据实际的火山云API文档调整）
        # 由于火山云API可能需要特殊配置，这里提供一个基础实现
        try:
            import requests

            # 使用当前配置的模型
            model = config.VOLCENGINE_MODEL

            # 转换消息格式
            volcengine_messages = []
            for msg in messages:
                volcengine_messages.append({
                    'role': msg['role'],
                    'content': msg['content']
                })

            # 使用客户端配置
            client_config = self.clients['volcengine']

            headers = {
                'Authorization': f'Bearer {client_config["api_key"]}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': model,
                'messages': volcengine_messages,
                'temperature': config.AI_TEMPERATURE,
                'max_tokens': config.AI_MAX_TOKENS
            }

            response = requests.post(
                client_config['endpoint'],
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    if content and content.strip():
                        return content.strip()
            else:
                raise Exception(f"火山云API返回错误: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"火山云API调用失败: {e}")
            raise

        return None

    def _call_ai_api(self, messages: list) -> Optional[str]:
        """根据当前提供商调用相应的AI API"""
        try:
            if self.provider == 'openai':
                return self._call_openai(messages)
            elif self.provider == 'qwen':
                return self._call_qwen(messages)
            elif self.provider == 'volcengine':
                return self._call_volcengine(messages)
            else:
                raise Exception(f"不支持的AI提供商: {self.provider}")
        except Exception as e:
            logger.error(f"{self.provider} API调用异常: {e}")
            raise

    def _retry_with_backoff(self, func, *args, **kwargs):
        """带指数退避的重试机制"""
        for attempt in range(self.max_retries):
            try:
                result = func(*args, **kwargs)
                if result is not None:
                    return result
            except Exception as e:
                error_type = type(e).__name__
                error_msg = str(e)
                logger.error(f"API 调用失败，error_msg: {error_msg}")

                # 判断错误类型并应用相应的重试策略
                if "connection" in error_msg.lower() or "connect" in error_msg.lower():
                    logger.warning(f"{self.provider} 网络连接错误，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        # 连接错误使用更长的重试间隔
                        delay = self.retry_delay * (4 ** attempt) + random.uniform(1, 3)
                        logger.info(f"网络连接失败，等待 {delay:.2f} 秒后重试...")
                        time.sleep(delay)
                elif "rate" in error_msg.lower() or "limit" in error_msg.lower():
                    logger.warning(f"{self.provider} API 速率限制，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (3 ** attempt) + random.uniform(0, 2)
                        logger.info(f"等待 {delay:.2f} 秒后重试...")
                        time.sleep(delay)
                elif "timeout" in error_msg.lower():
                    logger.warning(f"{self.provider} API 超时，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (2 ** attempt)
                        time.sleep(delay)
                elif "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
                    logger.warning(f"{self.provider} SSL/证书错误，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (2 ** attempt) + random.uniform(0, 1)
                        time.sleep(delay)
                elif "dns" in error_msg.lower() or "resolve" in error_msg.lower():
                    logger.warning(f"{self.provider} DNS解析错误，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (3 ** attempt) + random.uniform(1, 2)
                        time.sleep(delay)
                elif "api" in error_msg.lower():
                    logger.warning(f"{self.provider} API 错误，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (2 ** attempt) + random.uniform(0, 1)
                        time.sleep(delay)
                else:
                    logger.error(f"{self.provider} API 未知错误，第 {attempt + 1} 次尝试: {e}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (2 ** attempt)
                        time.sleep(delay)

        logger.error(f"{self.provider} API 调用失败，已达到最大重试次数")
        return None

    def get_analysis(self, prompt: str, system_message: str = None) -> Optional[str]:
        """获取AI分析结果"""
        if not prompt or not prompt.strip():
            logger.error("提示词为空")
            return None

        # 默认系统消息
        if system_message is None:
            system_message = "你是一个专业的ETF投资顾问专家，具有丰富的市场分析经验。请提供专业、客观的投资建议。"

        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": prompt}
        ]

        logger.info(f"开始调用{self.provider} API进行分析")
        logger.info(f"Promot messages : {messages}")
        result = self._retry_with_backoff(self._call_ai_api, messages)

        if result:
            logger.info(f"{self.provider}分析完成")
            return result
        else:
            logger.error(f"{self.provider}分析失败")
            return f"抱歉，{config.AI_MODELS.get(self.provider, {}).get('display_name', 'AI')}分析服务暂时不可用，请稍后重试。"

    def switch_provider(self, provider: str) -> bool:
        """切换AI提供商"""
        if provider in self.clients:
            self.provider = provider
            config.set_ai_provider(provider)
            logger.info(f"已切换到{config.AI_MODELS.get(provider, {}).get('display_name', provider)}")
            return True
        else:
            logger.error(f"AI提供商 {provider} 不可用")
            return False

    def get_available_providers(self) -> Dict[str, str]:
        """获取可用的AI提供商"""
        available = {}
        for provider in self.clients.keys():
            if provider in config.AI_MODELS:
                available[provider] = config.AI_MODELS[provider]['display_name']
        return available

# 全局多AI助手实例
multi_ai_helper = MultiAIHelper()

def get_ai_analysis(prompt: str, system_message: str = None, provider: str = None) -> str:
    """获取AI分析的主函数"""
    if provider and provider != multi_ai_helper.provider:
        multi_ai_helper.switch_provider(provider)
    
    result = multi_ai_helper.get_analysis(prompt, system_message)
    return result or "AI分析服务暂时不可用"
