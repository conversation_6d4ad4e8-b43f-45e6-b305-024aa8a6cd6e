"""
多AI模型助手
支持OpenAI、千问、火山云等多个AI提供商
"""
import os
import time
import random
import logging
import requests
import json
from typing import Optional, Dict, Any, List

# 配置日志
logger = logging.getLogger(__name__)

class MultiAIHelper:
    """多AI模型助手类，支持多个提供商"""

    def __init__(self, provider: str = None, max_retries: int = 3, retry_delay: float = 1.0):
        self.provider = provider or "openai"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.temperature = 0.7
        self.max_tokens = 2000
        
        # 初始化客户端
        self.clients = {}
        self._init_clients()

    def _init_clients(self):
        """初始化各个AI提供商的客户端"""
        # OpenAI客户端
        try:
            from openai import OpenAI
            api_key = os.getenv('OPENAI_API_KEY')
            base_url = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
            if api_key:
                self.clients['openai'] = OpenAI(
                    api_key=api_key,
                    base_url=base_url
                )
                logger.info("OpenAI客户端初始化成功")
        except ImportError:
            logger.warning("OpenAI库未安装，无法使用OpenAI模型")
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {e}")

        # 千问客户端
        try:
            import dashscope
            api_key = os.getenv('QWEN_API_KEY')
            if api_key:
                dashscope.api_key = api_key
                self.clients['qwen'] = dashscope
                logger.info("千问客户端初始化成功")
            else:
                logger.info("千问API密钥未配置，跳过初始化")
        except ImportError:
            logger.info("dashscope库未安装，无法使用千问模型")
        except Exception as e:
            logger.warning(f"千问客户端初始化失败: {e}")

        # 火山云客户端
        try:
            api_key = os.getenv('VOLCENGINE_API_KEY')
            endpoint = os.getenv('VOLCENGINE_ENDPOINT', 'https://ark.cn-beijing.volces.com/api/v3/chat/completions')
            if api_key:
                self.clients['volcengine'] = {
                    'api_key': api_key,
                    'endpoint': endpoint
                }
                logger.info(f"火山云客户端初始化成功，端点: {endpoint}")
            else:
                logger.info("火山云API密钥未配置，跳过初始化")
        except Exception as e:
            logger.warning(f"火山云客户端初始化失败: {e}")

    def _call_openai(self, messages: list) -> Optional[str]:
        """调用OpenAI API"""
        if 'openai' not in self.clients:
            raise Exception("OpenAI客户端未初始化")

        client = self.clients['openai']
        model = os.getenv('GPT_MODEL', 'gpt-4.1-mini')

        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            timeout=30
        )

        if response and response.choices and len(response.choices) > 0:
            content = response.choices[0].message.content
            if content and content.strip():
                return content.strip()
        
        return None

    def _call_qwen(self, messages: list) -> Optional[str]:
        """调用千问API"""
        if 'qwen' not in self.clients:
            raise Exception("千问客户端未初始化")

        dashscope = self.clients['qwen']
        model = os.getenv('QWEN_MODEL', 'qwen3-30b-a3b')

        try:
            response = dashscope.Generation.call(
                model=model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                result_format='message',
                enable_thinking=False  # qwen3系列模型需要此参数
            )

            if response and response.status_code == 200:
                output = response.output
                if output and 'choices' in output and len(output['choices']) > 0:
                    content = output['choices'][0]['message']['content']
                    if content and content.strip():
                        return content.strip()
        except Exception as e:
            logger.error(f"千问API调用失败: {e}")
            raise

        return None

    def _call_volcengine(self, messages: list) -> Optional[str]:
        """调用火山云API"""
        if 'volcengine' not in self.clients:
            raise Exception("火山云客户端未初始化")

        client_config = self.clients['volcengine']
        model = os.getenv('VOLCENGINE_MODEL', 'ep-20241211155058-8xqzr')

        headers = {
            'Authorization': f'Bearer {client_config["api_key"]}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': model,
            'messages': messages,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens
        }

        try:
            response = requests.post(
                client_config['endpoint'],
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    if content and content.strip():
                        return content.strip()
            else:
                logger.error(f"火山云API返回错误: {response.status_code}, {response.text}")
                raise Exception(f"火山云API调用失败: {response.status_code}")

        except requests.exceptions.RequestException as e:
            logger.error(f"火山云API请求失败: {e}")
            raise

        return None

    def get_available_providers(self) -> Dict[str, str]:
        """获取可用的AI提供商"""
        providers = {}
        
        if 'openai' in self.clients:
            providers['openai'] = 'OpenAI GPT'
        if 'qwen' in self.clients:
            providers['qwen'] = '千问'
        if 'volcengine' in self.clients:
            providers['volcengine'] = '火山云'
            
        return providers

    def call_ai(self, prompt: str, provider: str = None) -> Optional[str]:
        """调用AI模型"""
        provider = provider or self.provider
        
        if provider not in self.clients:
            available = list(self.clients.keys())
            if not available:
                raise Exception("没有可用的AI提供商")
            provider = available[0]
            logger.warning(f"指定的提供商 {provider} 不可用，使用 {provider}")

        messages = [{"role": "user", "content": prompt}]

        for attempt in range(self.max_retries):
            try:
                if provider == 'openai':
                    return self._call_openai(messages)
                elif provider == 'qwen':
                    return self._call_qwen(messages)
                elif provider == 'volcengine':
                    return self._call_volcengine(messages)
                else:
                    raise Exception(f"不支持的AI提供商: {provider}")

            except Exception as e:
                logger.error(f"AI调用失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    delay = self.retry_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"等待 {delay:.2f} 秒后重试...")
                    time.sleep(delay)
                else:
                    # 最后一次尝试失败，尝试其他提供商
                    available_providers = [p for p in self.clients.keys() if p != provider]
                    if available_providers:
                        fallback_provider = available_providers[0]
                        logger.info(f"尝试备用提供商: {fallback_provider}")
                        try:
                            if fallback_provider == 'openai':
                                return self._call_openai(messages)
                            elif fallback_provider == 'qwen':
                                return self._call_qwen(messages)
                            elif fallback_provider == 'volcengine':
                                return self._call_volcengine(messages)
                        except Exception as fallback_e:
                            logger.error(f"备用提供商也失败: {fallback_e}")

        return None

    def set_provider(self, provider: str):
        """设置AI提供商"""
        if provider in self.clients:
            self.provider = provider
            logger.info(f"AI提供商已切换到: {provider}")
        else:
            raise Exception(f"提供商 {provider} 不可用")

    def set_parameters(self, temperature: float = None, max_tokens: int = None):
        """设置AI参数"""
        if temperature is not None:
            self.temperature = temperature
        if max_tokens is not None:
            self.max_tokens = max_tokens

# 全局实例
multi_ai_helper = MultiAIHelper()

def get_ai_analysis(prompt: str, provider: str = None) -> Optional[str]:
    """获取AI分析（兼容性函数）"""
    return multi_ai_helper.call_ai(prompt, provider)
