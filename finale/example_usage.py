"""
Finale Supplier Migration 使用示例
演示如何使用迁移工具的各种功能
"""

import os
import logging
from finale import (
    create_api_client, 
    create_database_manager, 
    create_data_mapper,
    config
)
from finale.MigrateFinaleSupplierData import FinaleSupplierMigration


def example_1_test_connections():
    """示例1: 测试API和数据库连接"""
    print("=" * 50)
    print("示例1: 测试连接")
    print("=" * 50)
    
    # 测试API连接
    api_client = create_api_client()
    if api_client.test_connection():
        print("✓ Finale API连接成功")
    else:
        print("✗ Finale API连接失败")
    
    # 测试数据库连接
    db_manager = create_database_manager()
    if db_manager.test_connection():
        print("✓ 数据库连接成功")
    else:
        print("✗ 数据库连接失败")


def example_2_fetch_sample_data():
    """示例2: 获取样本数据"""
    print("=" * 50)
    print("示例2: 获取样本数据")
    print("=" * 50)
    
    api_client = create_api_client()
    
    # 获取partygroup列表
    party_data = api_client.get_party_group_list()
    if party_data:
        party_ids = party_data.get('partyId', [])
        print(f"获取到 {len(party_ids)} 个supplier")
        
        # 显示前5个supplier ID
        for i, party_id in enumerate(party_ids[:5]):
            print(f"  {i+1}. {party_id}")
        
        # 获取第一个supplier的详细信息
        if party_data.get('partyUrl'):
            detail = api_client.get_party_detail(party_data['partyUrl'][0])
            if detail:
                print(f"\n第一个supplier详细信息示例:")
                print(f"  Settlement Term: {detail.get('settlementTermId', 'N/A')}")
                print(f"  Lead Time: {detail.get('leadTime', 'N/A')}")
                print(f"  Description: {detail.get('description', 'N/A')}")
                
                contact_list = detail.get('contactMechList', [])
                print(f"  联系方式数量: {len(contact_list)}")
                
                user_fields = detail.get('userFieldDataList', [])
                print(f"  用户字段数量: {len(user_fields)}")
    else:
        print("获取supplier数据失败")


def example_3_data_mapping():
    """示例3: 数据映射演示"""
    print("=" * 50)
    print("示例3: 数据映射演示")
    print("=" * 50)
    
    # 创建测试数据
    test_supplier_data = {
        'partyId': '100001',
        'detail': {
            'settlementTermId': 'NET_15',
            'leadTime': 7,
            'description': 'Test supplier for demonstration',
            'contactMechList': [
                {
                    'contactMechTypeId': 'EMAIL_ADDRESS',
                    'infoString': '<EMAIL>'
                },
                {
                    'contactMechTypeId': 'POSTAL_ADDRESS',
                    'postalAddress': {
                        'address1': '123 Main Street',
                        'city': 'New York',
                        'stateProvinceGeoId': 'NY',
                        'postalCode': '10001',
                        'countryGeoId': 'US'
                    }
                },
                {
                    'contactMechTypeId': 'TELECOM_NUMBER',
                    'telecomNumber': {
                        'countryCode': '1',
                        'areaCode': '555',
                        'contactNumber': '1234567'
                    }
                }
            ],
            'userFieldDataList': [
                {
                    'userFieldId': 'user_10000',
                    'fieldValue': 'Group A'
                },
                {
                    'userFieldId': 'user_10004',
                    'fieldValue': 'true'
                }
            ]
        }
    }
    
    # 执行数据映射
    data_mapper = create_data_mapper()
    mapped_data = data_mapper.map_supplier_data(test_supplier_data)
    
    print("原始数据映射结果:")
    print(f"Vendor数据: {mapped_data['vendor_data']}")
    print(f"联系方式数据: {mapped_data['contact_mechanisms']}")
    print(f"额外信息数据: {mapped_data['additional_info']}")


def example_4_database_operations():
    """示例4: 数据库操作演示"""
    print("=" * 50)
    print("示例4: 数据库操作演示")
    print("=" * 50)
    
    db_manager = create_database_manager()
    
    # 查找vendor示例
    test_finale_id = "100001"
    vendor = db_manager.find_vendor_by_finale_id(test_finale_id)
    
    if vendor:
        print(f"找到vendor: {vendor['id']}")
        print(f"  名称: {vendor.get('vendor_name', 'N/A')}")
        print(f"  创建时间: {vendor.get('created_at', 'N/A')}")
    else:
        print(f"未找到finale_id为 {test_finale_id} 的vendor")


def example_5_dry_run_migration():
    """示例5: 干运行迁移演示"""
    print("=" * 50)
    print("示例5: 干运行迁移演示")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建迁移实例（干运行模式）
    migration = FinaleSupplierMigration(dry_run=True)
    
    print("开始干运行迁移...")
    success = migration.run_migration()
    
    if success:
        print("干运行完成，无错误")
    else:
        print("干运行发现错误，请检查日志")
    
    # 显示统计信息
    migration.print_statistics()


def example_6_configuration_demo():
    """示例6: 配置演示"""
    print("=" * 50)
    print("示例6: 配置信息演示")
    print("=" * 50)
    
    print("数据库配置:")
    print(f"  主机: {config.DB_CONFIG['host']}")
    print(f"  端口: {config.DB_CONFIG['port']}")
    print(f"  数据库: {config.DB_CONFIG['database']}")
    
    print("\nAPI配置:")
    print(f"  基础URL: {config.FINALE_API_CONFIG['base_url']}")
    print(f"  超时时间: {config.FINALE_API_CONFIG['timeout']}秒")
    
    print("\n映射配置示例:")
    from finale.config import data_mapping
    print(f"  NET_15 -> {data_mapping.get_settlement_term('NET_15')}")
    print(f"  user_10000 -> {data_mapping.get_user_field_name('user_10000')}")
    print(f"  POSTAL_ADDRESS -> {data_mapping.get_contact_mech_table('POSTAL_ADDRESS')}")


def main():
    """主函数 - 运行所有示例"""
    print("Finale Supplier Migration 使用示例")
    print("=" * 50)
    
    # 检查环境变量
    required_env_vars = ['DB_USERNAME', 'DB_PASSWORD', 'DB_HOST', 'DB_NAME']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print("警告: 以下环境变量未设置:")
        for var in missing_vars:
            print(f"  - {var}")
        print("\n某些示例可能无法正常运行。")
        print("请设置环境变量后重新运行。")
        print()
    
    try:
        # 运行各个示例
        example_6_configuration_demo()
        
        if not missing_vars:
            example_1_test_connections()
            example_2_fetch_sample_data()
            example_4_database_operations()
        
        example_3_data_mapping()
        
        # 注意: example_5_dry_run_migration() 会实际调用API，
        # 如果不想执行可以注释掉
        # example_5_dry_run_migration()
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")
        print("请检查配置和网络连接。")


if __name__ == "__main__":
    main()
