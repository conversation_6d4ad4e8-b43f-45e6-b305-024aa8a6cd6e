"""
数据映射器
用于将Finale数据转换为数据库格式
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from .config import data_mapping

logger = logging.getLogger(__name__)


class DataMapper:
    """数据映射器类"""
    
    def __init__(self):
        self.mapping = data_mapping
    
    def map_vendor_data(self, supplier_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        映射vendor表数据
        
        Args:
            supplier_data: Finale supplier数据
        
        Returns:
            Dict: 映射后的vendor数据
        """
        detail = supplier_data.get('detail', {})
        vendor_data = {}
        
        # 映射default_terms
        settlement_term_id = detail.get('settlementTermId')
        if settlement_term_id:
            vendor_data['default_terms'] = self.mapping.get_settlement_term(settlement_term_id)
        
        # 映射default_lead_days
        lead_time = detail.get('leadTime')
        if lead_time is not None:
            try:
                vendor_data['default_lead_days'] = int(lead_time)
            except (ValueError, TypeError):
                logger.warning(f"无效的leadTime值: {lead_time}")
        
        # 映射notes
        description = detail.get('description')
        if description:
            vendor_data['notes'] = str(description)

        # 映射default_terms
        contact_name = detail.get('contactName')
        if contact_name is not None:
            vendor_data['vendor_contact_name'] = str(contact_name)
        
        return vendor_data
    
    def map_contact_mechanisms(self, supplier_data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        映射联系方式数据
        
        Args:
            supplier_data: Finale supplier数据
        
        Returns:
            Dict: 按类型分组的联系方式数据
        """
        detail = supplier_data.get('detail', {})
        contact_mech_list = detail.get('contactMechList', [])
        
        mapped_contacts = {
            'address': [],
            'email': [],
            'phone_number': [],
            'web_address': []
        }
        
        for contact_mech in contact_mech_list:
            mech_type = contact_mech.get('contactMechTypeId')
            table_name = self.mapping.get_contact_mech_table(mech_type)
            
            if not table_name:
                logger.warning(f"未知的contactMechTypeId: {mech_type}")
                continue
            
            if table_name == 'address':
                address_data = self._map_address_data(contact_mech)
                if address_data:
                    mapped_contacts['address'].append(address_data)
            
            elif table_name == 'email':
                email_data = self._map_email_data(contact_mech)
                if email_data:
                    mapped_contacts['email'].append(email_data)
            
            elif table_name == 'phone_number':
                phone_data = self._map_phone_data(contact_mech)
                if phone_data:
                    mapped_contacts['phone_number'].append(phone_data)
            
            elif table_name == 'web_address':
                web_data = self._map_web_address_data(contact_mech)
                if web_data:
                    mapped_contacts['web_address'].append(web_data)
        
        return mapped_contacts
    
    def _map_address_data(self, contact_mech: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """映射地址数据"""
        postal_address = contact_mech.get('postalAddress', {})
        
        # 检查必填字段
        address1 = postal_address.get('address1', '').strip()
        city = postal_address.get('city', '').strip()
        
        if not address1 or len(address1) < 5:
            logger.warning("地址信息不完整，跳过")
            return None
        
        if not city or len(city) < 2:
            logger.warning("城市信息不完整，跳过")
            return None
        
        # 构建完整地址
        address_parts = [address1]
        address2 = postal_address.get('address2', '').strip()
        if address2:
            address_parts.append(address2)
        
        return {
            'street_address': ', '.join(address_parts),
            'city': city,
            'state': postal_address.get('stateProvinceGeoId'),
            'postal_code': postal_address.get('postalCode'),
            'country': postal_address.get('countryGeoId'),
            'directions': postal_address.get('directions'),
            'purpose': 'BUSINESS',
            'additional_lines': postal_address.get('address2') if address2 else None
        }
    
    def _map_email_data(self, contact_mech: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """映射邮箱数据"""
        email_address = contact_mech.get('infoString', '').strip()
        
        if not email_address or '@' not in email_address:
            logger.warning("邮箱地址无效，跳过")
            return None
        
        if len(email_address) < 5 or len(email_address) > 255:
            logger.warning("邮箱地址长度不符合要求，跳过")
            return None
        
        return {
            'email_type': 'BUSINESS',
            'email': email_address,
            'extension': None
        }
    
    def _map_phone_data(self, contact_mech: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """映射电话数据"""
        telecom_number = contact_mech.get('telecomNumber', {})
        
        # 构建完整电话号码
        country_code = telecom_number.get('countryCode', '').strip()
        area_code = telecom_number.get('areaCode', '').strip()
        contact_number = telecom_number.get('contactNumber', '').strip()
        
        if not contact_number:
            logger.warning("电话号码为空，跳过")
            return None
        
        # 组合电话号码
        phone_parts = []
        if country_code:
            phone_parts.append(f"+{country_code}")
        if area_code:
            phone_parts.append(f"({area_code})")
        phone_parts.append(contact_number)
        
        full_phone = ' '.join(phone_parts)
        
        if len(full_phone) < 7 or len(full_phone) > 20:
            logger.warning("电话号码长度不符合要求，跳过")
            return None
        
        return {
            'phone_type': 'BUSINESS',
            'phone_number': full_phone,
            'extension': telecom_number.get('extension')
        }
    
    def _map_web_address_data(self, contact_mech: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """映射网址数据"""
        web_address = contact_mech.get('infoString', '').strip()
        
        if not web_address:
            logger.warning("网址为空，跳过")
            return None
        
        # 简单的URL验证
        if not (web_address.startswith('http://') or web_address.startswith('https://') or web_address.startswith('www.')):
            # 如果没有协议前缀，添加http://
            web_address = f"http://{web_address}"
        
        if len(web_address) > 500:
            logger.warning("网址长度超过限制，跳过")
            return None
        
        return {
            'web_address_type': 'WEBSITE',
            'web_address': web_address
        }
    
    def map_supplier_additional_info(self, supplier_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        映射supplier额外信息
        
        Args:
            supplier_data: Finale supplier数据
        
        Returns:
            Dict: 映射后的额外信息数据
        """
        detail = supplier_data.get('detail', {})
        user_field_data_list = detail.get('userFieldDataList', [])
        
        additional_info = {}
        
        for user_field in user_field_data_list:
            field_id = user_field.get('userFieldId')
            field_value = user_field.get('fieldValue')
            
            if not field_id:
                continue
            
            # 获取映射的字段名
            mapped_field = self.mapping.get_user_field_name(field_id)
            
            if mapped_field == field_id:  # 没有找到映射
                logger.warning(f"未知的userFieldId: {field_id}")
                continue
            
            # 处理布尔值字段
            if self.mapping.is_boolean_field(mapped_field):
                additional_info[mapped_field] = self._convert_to_boolean(field_value)
            else:
                additional_info[mapped_field] = field_value
        
        return additional_info
    
    def _convert_to_boolean(self, value: Any) -> bool:
        """将值转换为布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', 'y')
        if isinstance(value, (int, float)):
            return bool(value)
        return False
    
    def map_supplier_data(self, supplier_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        映射完整的supplier数据
        
        Args:
            supplier_data: Finale supplier数据
        
        Returns:
            Dict: 包含所有映射数据的字典
        """
        return {
            'vendor_data': self.map_vendor_data(supplier_data),
            'contact_mechanisms': self.map_contact_mechanisms(supplier_data),
            'additional_info': self.map_supplier_additional_info(supplier_data)
        }


# 便捷函数
def create_data_mapper() -> DataMapper:
    """创建数据映射器实例"""
    return DataMapper()


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    mapper = create_data_mapper()
    
    # 测试数据
    test_data = {
        'partyId': '100001',
        'detail': {
            'settlementTermId': 'NET_15',
            'leadTime': 7,
            'description': 'Test supplier',
            'contactMechList': [
                {
                    'contactMechTypeId': 'EMAIL_ADDRESS',
                    'infoString': '<EMAIL>'
                }
            ],
            'userFieldDataList': [
                {
                    'userFieldId': 'user_10004',
                    'fieldValue': 'true'
                }
            ]
        }
    }
    
    mapped_data = mapper.map_supplier_data(test_data)
    print("映射结果:", mapped_data)
