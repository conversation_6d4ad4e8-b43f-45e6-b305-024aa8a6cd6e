# Finale Supplier Data Migration

从Finale API导入supplier数据到PostgreSQL数据库的迁移工具。

## 功能特性

- 从Finale API获取完整的supplier数据
- 智能数据映射和转换
- 支持多种联系方式类型（地址、邮箱、电话、网址）
- 完整的错误处理和日志记录
- 支持dry-run模式预览操作
- 事务安全的数据库操作

## 系统要求

- Python 3.7+
- PostgreSQL数据库
- 网络访问Finale API

## 安装依赖

```bash
pip install sqlalchemy psycopg2-binary requests
```

## 配置

### 1. 环境变量配置

在运行脚本前，请设置以下环境变量：

```bash
export DB_USERNAME="your_db_username"
export DB_PASSWORD="your_db_password"
export DB_HOST="your_db_host"
export DB_PORT="5432"
export DB_NAME="your_database_name"
```

### 2. 数据库表结构

确保数据库中存在以下表：
- `vendor` - 供应商主表
- `address` - 地址表
- `email` - 邮箱表
- `phone_number` - 电话表
- `web_address` - 网址表
- `supplier_additional_info` - 供应商额外信息表

## 使用方法

### 基本用法

```bash
# 直接运行迁移
python -m finale.MigrateFinaleSupplierData

# 预览模式（不实际修改数据库）
python -m finale.MigrateFinaleSupplierData --dry-run

# 详细日志模式
python -m finale.MigrateFinaleSupplierData --verbose

# 组合使用
python -m finale.MigrateFinaleSupplierData --dry-run --verbose
```

### 编程方式使用

```python
from finale import FinaleSupplierMigration

# 创建迁移实例
migration = FinaleSupplierMigration(dry_run=False)
migration.setup_logging(verbose=True)

# 运行迁移
success = migration.run_migration()

if success:
    print("迁移成功！")
else:
    print("迁移失败，请查看日志。")
```

## 数据映射关系

### Vendor表映射

| Finale字段 | 数据库字段 | 说明 |
|-----------|-----------|------|
| partyId | finale_id | Finale系统ID |
| settlementTermId | default_terms | 结算条款 |
| leadTime | default_lead_days | 默认交货天数 |
| description | notes | 备注信息 |

### 联系方式映射

| Finale类型 | 数据库表 | 说明 |
|-----------|---------|------|
| POSTAL_ADDRESS | address | 邮政地址 |
| EMAIL_ADDRESS | email | 邮箱地址 |
| TELECOM_NUMBER | phone_number | 电话号码 |
| WEB_ADDRESS | web_address | 网站地址 |

### 用户字段映射

| Finale字段ID | 数据库字段 | 说明 |
|-------------|-----------|------|
| user_10009 | destination_dc | 目标配送中心 |
| user_10000 | purchasing_group | 采购组 |
| user_10001 | account_num | 账户号码 |
| user_10002 | order_method | 订单方式 |
| user_10003 | freight | 运费方式 |
| user_10004 | delivery_mon | 周一配送 |
| user_10005 | delivery_tue | 周二配送 |
| user_10006 | delivery_wed | 周三配送 |
| user_10007 | delivery_thu | 周四配送 |
| user_10008 | delivery_fri | 周五配送 |

## 迁移逻辑

1. **获取数据**: 从Finale API获取所有supplier数据
2. **查找匹配**: 根据partyId在vendor表中查找对应记录
3. **跳过不存在**: 如果vendor不存在则跳过该supplier
4. **更新vendor**: 更新vendor表的相关字段
5. **删除旧数据**: 软删除现有的联系方式和额外信息
6. **插入新数据**: 插入新的联系方式和额外信息

## 日志和监控

- 日志文件: `finale_migration.log`
- 支持控制台和文件双重输出
- 详细的统计信息输出
- 错误追踪和报告

## 错误处理

- API连接失败自动重试
- 数据库事务回滚保护
- 详细的错误日志记录
- 优雅的错误恢复机制

## 安全考虑

- 使用参数化查询防止SQL注入
- 事务保护确保数据一致性
- 敏感信息通过环境变量配置
- 软删除保护历史数据

## 性能优化

- 批量数据处理
- 连接池管理
- 内存使用优化
- 进度监控和报告

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查环境变量配置
   - 确认数据库服务状态
   - 验证网络连接

2. **API调用失败**
   - 检查网络连接
   - 验证API端点可访问性
   - 查看API响应错误信息

3. **数据映射错误**
   - 检查Finale数据格式
   - 验证映射配置
   - 查看详细日志信息

### 调试模式

使用`--verbose`参数获取详细的调试信息：

```bash
python -m finale.MigrateFinaleSupplierData --verbose --dry-run
```

## 版本历史

- v1.0.0: 初始版本，支持基本的数据迁移功能

## 支持

如有问题请联系开发团队或查看日志文件获取详细错误信息。
