"""
组件测试脚本
用于验证所有组件是否正常工作
"""

import sys
import os
import logging
from typing import Dict, Any

# 添加父目录到Python路径以支持包导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from finale.config import config, data_mapping, table_config
        print("✓ config模块导入成功")
    except ImportError as e:
        print(f"✗ config模块导入失败: {e}")
        return False

    try:
        from finale.finale_api_client import create_api_client
        print("✓ finale_api_client模块导入成功")
    except ImportError as e:
        print(f"✗ finale_api_client模块导入失败: {e}")
        return False

    try:
        from finale.database_manager import create_database_manager
        print("✓ database_manager模块导入成功")
    except ImportError as e:
        print(f"✗ database_manager模块导入失败: {e}")
        return False

    try:
        from finale.data_mapper import create_data_mapper
        print("✓ data_mapper模块导入成功")
    except ImportError as e:
        print(f"✗ data_mapper模块导入失败: {e}")
        return False

    try:
        from finale.MigrateFinaleSupplierData import FinaleSupplierMigration
        print("✓ MigrateFinaleSupplierData模块导入成功")
    except ImportError as e:
        print(f"✗ MigrateFinaleSupplierData模块导入失败: {e}")
        return False
    
    return True


def test_config():
    """测试配置功能"""
    print("\n测试配置功能...")
    
    try:
        from finale.config import config, data_mapping, table_config
        
        # 测试数据库配置
        db_string = config.get_db_connection_string()
        print(f"✓ 数据库连接字符串生成成功")
        
        # 测试映射功能
        term = data_mapping.get_settlement_term('NET_15')
        print(f"✓ 结算条款映射: NET_15 -> {term}")
        
        field = data_mapping.get_user_field_name('user_10000')
        print(f"✓ 用户字段映射: user_10000 -> {field}")
        
        table = data_mapping.get_contact_mech_table('POSTAL_ADDRESS')
        print(f"✓ 联系方式表映射: POSTAL_ADDRESS -> {table}")
        
        is_bool = data_mapping.is_boolean_field('delivery_mon')
        print(f"✓ 布尔字段检查: delivery_mon -> {is_bool}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False


def test_data_mapper():
    """测试数据映射器"""
    print("\n测试数据映射器...")
    
    try:
        from finale.data_mapper import create_data_mapper
        
        mapper = create_data_mapper()
        
        # 创建测试数据
        test_data = {
            'partyId': '100001',
            'detail': {
                'settlementTermId': 'NET_15',
                'leadTime': 7,
                'description': 'Test supplier',
                'contactMechList': [
                    {
                        'contactMechTypeId': 'EMAIL_ADDRESS',
                        'infoString': '<EMAIL>'
                    },
                    {
                        'contactMechTypeId': 'POSTAL_ADDRESS',
                        'postalAddress': {
                            'address1': '123 Test Street',
                            'city': 'Test City',
                            'stateProvinceGeoId': 'TS',
                            'postalCode': '12345'
                        }
                    }
                ],
                'userFieldDataList': [
                    {
                        'userFieldId': 'user_10000',
                        'fieldValue': 'Test Group'
                    },
                    {
                        'userFieldId': 'user_10004',
                        'fieldValue': 'true'
                    }
                ]
            }
        }
        
        # 测试映射
        mapped_data = mapper.map_supplier_data(test_data)
        
        print("✓ 数据映射成功")
        print(f"  Vendor数据: {len(mapped_data['vendor_data'])} 个字段")
        print(f"  联系方式: {sum(len(v) for v in mapped_data['contact_mechanisms'].values())} 条记录")
        print(f"  额外信息: {len(mapped_data['additional_info'])} 个字段")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据映射器测试失败: {e}")
        return False


def test_api_client():
    """测试API客户端（不实际调用API）"""
    print("\n测试API客户端...")
    
    try:
        from finale.finale_api_client import create_api_client
        
        client = create_api_client()
        print("✓ API客户端创建成功")
        print(f"  基础URL: {client.base_url}")
        print(f"  超时时间: {client.timeout}秒")
        
        # 注意: 这里不实际测试连接，因为可能没有网络或API不可用
        print("  (跳过实际API连接测试)")
        
        return True
        
    except Exception as e:
        print(f"✗ API客户端测试失败: {e}")
        return False


def test_database_manager():
    """测试数据库管理器（不实际连接数据库）"""
    print("\n测试数据库管理器...")
    
    try:
        from finale.database_manager import create_database_manager
        
        # 只测试创建，不测试连接
        print("✓ 数据库管理器类可以创建")
        print("  (跳过实际数据库连接测试)")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库管理器测试失败: {e}")
        return False


def test_migration_class():
    """测试迁移类"""
    print("\n测试迁移类...")

    try:
        from finale.MigrateFinaleSupplierData import FinaleSupplierMigration

        print("✓ 迁移类导入成功")
        print("  (跳过实际迁移实例创建，避免数据库连接)")

        # 测试类的存在和基本属性
        print("✓ 迁移类可用")

        return True

    except Exception as e:
        print(f"✗ 迁移类测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Finale Supplier Migration 组件测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置功能", test_config),
        ("数据映射器", test_data_mapper),
        ("API客户端", test_api_client),
        ("数据库管理器", test_database_manager),
        ("迁移类", test_migration_class)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有组件测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
