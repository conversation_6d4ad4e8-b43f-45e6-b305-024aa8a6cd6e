"""
Finale Supplier Data Migration Script
从Finale API导入supplier数据到数据库

使用方法:
    python MigrateFinaleSupplierData.py [--dry-run] [--verbose]

参数:
    --dry-run: 只显示将要执行的操作，不实际修改数据库
    --verbose: 显示详细日志信息
"""

import argparse
import logging
import sys
from typing import Dict, List, Any
from datetime import datetime

from .config import config
from .finale_api_client import create_api_client
from .database_manager import create_database_manager
from .data_mapper import create_data_mapper


class FinaleSupplierMigration:
    """Finale Supplier数据迁移类"""

    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.api_client = create_api_client()
        self.db_manager = create_database_manager()
        self.data_mapper = create_data_mapper()

        # 统计信息
        self.stats = {
            'total_suppliers': 0,
            'processed_suppliers': 0,
            'updated_vendors': 0,
            'skipped_suppliers': 0,
            'errors': 0,
            'inserted_addresses': 0,
            'inserted_emails': 0,
            'inserted_phones': 0,
            'inserted_web_addresses': 0,
            'inserted_additional_info': 0
        }

        self.logger = logging.getLogger(__name__)

    def setup_logging(self, verbose: bool = False):
        """设置日志"""
        level = logging.DEBUG if verbose else logging.INFO

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)

        # 文件处理器
        file_handler = logging.FileHandler(config.LOG_CONFIG['file'])
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(config.LOG_CONFIG['format'])
        file_handler.setFormatter(file_formatter)

        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        root_logger.addHandler(console_handler)
        root_logger.addHandler(file_handler)

        self.logger.info("日志系统初始化完成")

    def validate_connections(self) -> bool:
        """验证API和数据库连接"""
        self.logger.info("验证连接...")

        # 测试API连接
        if not self.api_client.test_connection():
            self.logger.error("Finale API连接失败")
            return False

        # 测试数据库连接
        if not self.db_manager.test_connection():
            self.logger.error("数据库连接失败")
            return False

        self.logger.info("所有连接验证成功")
        return True

    def fetch_suppliers_data(self) -> List[Dict[str, Any]]:
        """获取所有supplier数据"""
        self.logger.info("开始获取Finale supplier数据...")

        suppliers_data = self.api_client.get_all_suppliers_data()
        self.stats['total_suppliers'] = len(suppliers_data)

        self.logger.info(f"成功获取 {self.stats['total_suppliers']} 个supplier数据")
        return suppliers_data

    def process_supplier(self, supplier_data: Dict[str, Any]) -> bool:
        """
        处理单个supplier

        Args:
            supplier_data: supplier数据

        Returns:
            bool: 处理是否成功
        """
        party_id = supplier_data.get('partyId')
        self.logger.info(f"处理supplier: {party_id}")

        try:
            # 查找对应的vendor
            vendor = self.db_manager.find_vendor_by_finale_id(party_id)
            if not vendor:
                self.logger.warning(f"未找到对应的vendor，跳过: {party_id}")
                self.stats['skipped_suppliers'] += 1
                return True

            vendor_id = vendor['id']
            self.logger.info(f"找到对应vendor: {vendor_id}")

            # 映射数据
            mapped_data = self.data_mapper.map_supplier_data(supplier_data)

            # 更新vendor信息
            if mapped_data['vendor_data']:
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] 将更新vendor: {vendor_id}, 数据: {mapped_data['vendor_data']}")
                else:
                    if self.db_manager.update_vendor(vendor_id, mapped_data['vendor_data']):
                        self.stats['updated_vendors'] += 1
                        self.logger.info(f"成功更新vendor: {vendor_id}")
                    else:
                        self.logger.error(f"更新vendor失败: {vendor_id}")
                        return False

            # 删除旧的联系方式和额外信息
            if not self.dry_run:
                self.db_manager.delete_vendor_contacts(vendor_id)
                self.db_manager.delete_supplier_additional_info(vendor_id)

            # 插入新的联系方式
            contact_mechanisms = mapped_data['contact_mechanisms']

            # 插入地址
            for address_data in contact_mechanisms['address']:
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] 将插入地址: {address_data}")
                else:
                    if self.db_manager.insert_address(vendor_id, address_data):
                        self.stats['inserted_addresses'] += 1
                    else:
                        self.logger.error(f"插入地址失败: {vendor_id}")

            # 插入邮箱
            for email_data in contact_mechanisms['email']:
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] 将插入邮箱: {email_data}")
                else:
                    if self.db_manager.insert_email(vendor_id, email_data):
                        self.stats['inserted_emails'] += 1
                    else:
                        self.logger.error(f"插入邮箱失败: {vendor_id}")

            # 插入电话
            for phone_data in contact_mechanisms['phone_number']:
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] 将插入电话: {phone_data}")
                else:
                    if self.db_manager.insert_phone_number(vendor_id, phone_data):
                        self.stats['inserted_phones'] += 1
                    else:
                        self.logger.error(f"插入电话失败: {vendor_id}")

            # 插入网址
            for web_data in contact_mechanisms['web_address']:
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] 将插入网址: {web_data}")
                else:
                    if self.db_manager.insert_web_address(vendor_id, web_data):
                        self.stats['inserted_web_addresses'] += 1
                    else:
                        self.logger.error(f"插入网址失败: {vendor_id}")

            # 插入额外信息
            additional_info = mapped_data['additional_info']
            if additional_info:
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] 将插入额外信息: {additional_info}")
                else:
                    if self.db_manager.insert_supplier_additional_info(vendor_id, additional_info):
                        self.stats['inserted_additional_info'] += 1
                    else:
                        self.logger.error(f"插入额外信息失败: {vendor_id}")

            self.stats['processed_suppliers'] += 1
            self.logger.info(f"成功处理supplier: {party_id}")
            return True

        except Exception as e:
            self.logger.error(f"处理supplier失败 {party_id}: {e}")
            self.stats['errors'] += 1
            return False

    def run_migration(self) -> bool:
        """运行完整的迁移流程"""
        start_time = datetime.now()
        self.logger.info("开始Finale Supplier数据迁移")

        if self.dry_run:
            self.logger.info("*** DRY RUN 模式 - 不会实际修改数据库 ***")

        try:
            # 验证连接
            if not self.validate_connections():
                return False

            # 获取数据
            suppliers_data = self.fetch_suppliers_data()
            if not suppliers_data:
                self.logger.error("未获取到任何supplier数据")
                return False

            # 处理每个supplier
            self.logger.info(f"开始处理 {len(suppliers_data)} 个supplier...")

            for i, supplier_data in enumerate(suppliers_data, 1):
                self.logger.info(f"进度: {i}/{len(suppliers_data)}")
                self.process_supplier(supplier_data)

            # 输出统计信息
            self.print_statistics()

            end_time = datetime.now()
            duration = end_time - start_time
            self.logger.info(f"迁移完成，耗时: {duration}")

            return self.stats['errors'] == 0

        except Exception as e:
            self.logger.error(f"迁移过程中发生错误: {e}")
            return False

    def print_statistics(self):
        """打印统计信息"""
        self.logger.info("=" * 50)
        self.logger.info("迁移统计信息:")
        self.logger.info(f"总supplier数量: {self.stats['total_suppliers']}")
        self.logger.info(f"已处理supplier: {self.stats['processed_suppliers']}")
        self.logger.info(f"跳过的supplier: {self.stats['skipped_suppliers']}")
        self.logger.info(f"更新的vendor: {self.stats['updated_vendors']}")
        self.logger.info(f"插入的地址: {self.stats['inserted_addresses']}")
        self.logger.info(f"插入的邮箱: {self.stats['inserted_emails']}")
        self.logger.info(f"插入的电话: {self.stats['inserted_phones']}")
        self.logger.info(f"插入的网址: {self.stats['inserted_web_addresses']}")
        self.logger.info(f"插入的额外信息: {self.stats['inserted_additional_info']}")
        self.logger.info(f"错误数量: {self.stats['errors']}")
        self.logger.info("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Finale Supplier数据迁移工具')
    parser.add_argument('--dry-run', action='store_true',
                       help='只显示将要执行的操作，不实际修改数据库')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细日志信息')

    args = parser.parse_args()

    # 创建迁移实例
    migration = FinaleSupplierMigration(dry_run=args.dry_run)
    migration.setup_logging(verbose=args.verbose)

    # 运行迁移
    success = migration.run_migration()

    if success:
        print("迁移成功完成！")
        sys.exit(0)
    else:
        print("迁移过程中出现错误，请查看日志文件。")
        sys.exit(1)


if __name__ == "__main__":
    main()