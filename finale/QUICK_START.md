# Finale Supplier Migration - 快速开始指南

## 1. 安装依赖

```bash
cd finale
pip install -r requirements.txt
```

## 2. 配置环境变量

```bash
# 设置数据库连接信息
export DB_USERNAME="your_db_username"
export DB_PASSWORD="your_db_password"
export DB_HOST="your_db_host"
export DB_PORT="5432"
export DB_NAME="your_database_name"
```

## 3. 测试组件

```bash
# 运行组件测试
python test_components.py
```

## 4. 预览迁移（推荐）

```bash
# 干运行模式 - 只显示将要执行的操作，不实际修改数据库
python -m finale.MigrateFinaleSupplierData --dry-run --verbose
```

## 5. 执行实际迁移

```bash
# 执行实际迁移
python -m finale.MigrateFinaleSupplierData --verbose
```

## 6. 查看日志

迁移过程中的详细日志会保存在 `finale_migration.log` 文件中。

## 重要提醒

1. **首次使用请务必先运行 `--dry-run` 模式**
2. **确保数据库已备份**
3. **验证网络可以访问 Finale API**
4. **确认 vendor 表中存在对应的 finale_id 记录**

## 故障排除

- 如果遇到导入错误，请确保在项目根目录运行命令
- 如果数据库连接失败，请检查环境变量配置
- 如果 API 调用失败，请检查网络连接和 API 端点

## 示例输出

成功运行后，你会看到类似以下的统计信息：

```
==================================================
迁移统计信息:
总supplier数量: 10
已处理supplier: 8
跳过的supplier: 2
更新的vendor: 8
插入的地址: 5
插入的邮箱: 8
插入的电话: 6
插入的网址: 2
插入的额外信息: 8
错误数量: 0
==================================================
```
